ActsAsList
==========

This acts_as extension provides the capabilities for sorting and reordering a number of objects in a list. The class that has this specified needs to have a +position+ column defined as an integer on the mapped database table.


Example
=======

  class TodoList < ActiveRecord::Base
    has_many :todo_items, :order => "position"
  end

  class TodoItem < ActiveRecord::Base
    belongs_to :todo_list
    acts_as_list :scope => :todo_list
  end

  todo_list.first.move_to_bottom
  todo_list.last.insert_at(2)


Copyright (c) 2007 <PERSON>, released under the MIT license