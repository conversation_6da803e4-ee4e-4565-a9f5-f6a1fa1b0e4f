development:
  url: redis://redis

test:
  # only tests that are exercising the integration with redis require redis to run.
  # warning: the redis database will get cleared before each test, so if you
  # use this server for anything else, make sure to set aside a database id for
  # these tests to use.
  url: redis://<%= ENV['REDIS_HOST'] || 'redis' %>:<%= ENV['REDIS_PORT'] || 6379 %>/1
  connect_timeout: 0.5
  circuit_breaker:
    error_threshold: 1
    error_timeout: 2

