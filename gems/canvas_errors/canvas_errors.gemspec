# frozen_string_literal: true

Gem::Specification.new do |spec|
  spec.name          = "canvas_errors"
  spec.version       = "0.1.0"
  spec.authors       = ["<PERSON>"]
  spec.email         = ["<EMAIL>"]
  spec.summary       = "Instructure gem for capturing errors to arbitrary reporting destinations"

  spec.files         = Dir.glob("{lib,spec}/**/*") + %w[test.sh]
  spec.executables   = spec.files.grep(%r{^bin/}) { |f| File.basename(f) }
  spec.require_paths = ["lib"]

  spec.add_dependency "activesupport"
  spec.add_dependency "code_ownership"
  spec.add_dependency "inst-jobs"
end
