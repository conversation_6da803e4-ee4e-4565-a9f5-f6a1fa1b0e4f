## TODO

  - figure out whether an answer is present in a response to a question (should be overridable by each question type if needed) _[DONE: 11/05/2014]_
  - port the stats generation from QuizQuestion::Base#stats into the generic answer analyzer
  - question statistics support:
    + Essay _[DONE: 29/04/2014]_
    + Fill in Multiple Blanks
    + Fill in The Blank (`short_answer`)
    + Matching
    + Multiple Answers
    + Multiple Choice
    + Multiple Dropdowns
    + Numerical
    + True/False
