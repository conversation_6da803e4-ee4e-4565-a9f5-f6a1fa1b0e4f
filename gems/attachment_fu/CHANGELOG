* Apr 17 2008 *
* amazon_s3.yml is now passed through ERB before being passed to AWS::S3 [<PERSON>]

* Mar 22 2008 *
* Some tweaks to support Rails 2.0 and Rails 2.1 due to ActiveSupport::Callback changes.  
  Thanks to http://blog.methodmissing.com/2008/1/19/edge-callback-refactorings-attachment_fu/

* Feb. 26, 2008 *
* remove breakpoint from test_helper, makes test suite crazy (at least Rails 2+) [<PERSON>]
* make S3 test really optional [<PERSON>]

* Nov 27, 2007 *
* Handle properly ImageScience thumbnails resized from a gif file [<PERSON>]
* Save thumbnails file size properly when using ImageScience [<PERSON>]
* fixed s3 config file loading with latest versions of Rails [<PERSON>]

* April 2, 2007 *

* don't copy the #full_filename to the default #temp_paths array if it doesn't exist
* add default ID partitioning for attachments
* add #binmode call to Tempfile (note: ruby should be doing this!) [<PERSON>]
* Check for current type of :thumbnails option.
* allow customization of the S3 configuration file path with the :s3_config_path option.
* Don't try to remove thumbnails if there aren't any.  Closes #3 [ben stiglitz]

* BC * (before changelog)

* add default #temp_paths entry [mattly]
* add MiniMagick support to attachment_fu [Isacc]
* update #destroy_file to clear out any empty directories too [carlivar]
* fix references to S3Backend module [Hunter Hillegas]
* make #current_data public with db_file and s3 backends [ebryn]
* oops, actually svn add the files for s3 backend. [Jeffrey Hardy]
* experimental s3 support, egad, no tests.... [Jeffrey Hardy]
* doh, fix a few bad references to ActsAsAttachment [sixty4bit]
