# frozen_string_literal: true

Gem::Specification.new do |spec|
  spec.name          = "bookmarked_collection"
  spec.version       = "1.0.0"
  spec.authors       = ["<PERSON>", "<PERSON>"]
  spec.email         = ["<EMAIL>", "<EMAIL>"]
  spec.summary       = "Bookmarked collections for Canvas"

  spec.files         = Dir.glob("{lib}/**/*") + %w[Rakefile]
  spec.executables   = spec.files.grep(%r{^bin/}) { |f| File.basename(f) }
  spec.require_paths = ["lib"]

  spec.add_dependency "activerecord", ">= 3.2"
  spec.add_dependency "folio-pagination", "~> 0.0.12"
  spec.add_dependency "railties", ">= 3.2"
  spec.add_dependency "will_paginate", ">= 3.0", "< 5.0"

  spec.add_dependency "json_token"
  spec.add_dependency "paginated_collection"
end
