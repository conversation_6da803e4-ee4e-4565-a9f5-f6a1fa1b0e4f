MimetypeFu
==========

Some great Rails plugins like attachment_fu use the content type/mime type of a file to validate the instance of an object.
The plugin usually gets the mime type using the CGI request, however, if the file is already in the system, this approach won't work.
Adobe Flash is also known not to send the proper mime type.
As an alternative, I wrote mimetype_fu, a simple plugin which will try to guess the mimetype of a file based on its extension.

Note that mimetype_fu only looks at the extension to define its mime type if you are using Windows!

http://github.com/mattetti/mimetype-fu

Thanks to <PERSON><PERSON><PERSON><PERSON> for his big report and patch.

